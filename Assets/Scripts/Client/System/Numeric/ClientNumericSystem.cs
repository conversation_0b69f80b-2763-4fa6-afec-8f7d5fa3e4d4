using System.Collections.Generic;
using QFramework;
using TFG.Shared;
using GameClient.Command.Battle;
using Client.Model.Battle;
using GameClient.Model;

namespace GameClient.System.Numeric
{
    /// <summary>
    /// 客户端数值系统
    /// 负责管理多个玩家的数值数据，提供基础的存储和获取功能
    /// 数据统一存储在ClientBattleData中
    /// </summary>
    public class ClientNumericSystem : AbstractSystem
    {
        private IUnRegisterList mUnregisterList = new UnRegisterList();

        protected override void OnInit()
        {
            // 监听数值更新事件
            this.RegisterEvent<NumericUpdateEvent>(OnNumericUpdate).AddToUnregisterList(mUnregisterList);
        }

        /// <summary>
        /// 处理数值更新事件
        /// </summary>
        private void OnNumericUpdate(NumericUpdateEvent updateEvent)
        {
            var playerId = updateEvent.PlayerId;
            var numericType = updateEvent.NumericType;
            var value = updateEvent.Value;

            // 获取当前战斗数据
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                // 通过ClientBattleData设置玩家数值
                currentBattle.SetPlayerNumeric(playerId, numericType, value);
                
                // LogKit.I($"[ClientNumericSystem] 玩家 {playerId} 数值更新: Type={numericType}, Value={value}");
            }
            else
            {
                LogKit.W($"[ClientNumericSystem] 无当前战斗数据，无法更新玩家 {playerId} 的数值");
            }
        }

        /// <summary>
        /// 获取指定玩家的数值
        /// </summary>
        public long GetPlayerNumeric(string playerId, int numericType)
        {
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                return currentBattle.GetPlayerNumeric(playerId, numericType);
            }
            return 0;
        }

        /// <summary>
        /// 获取指定玩家的数值模型
        /// </summary>
        public NumericModel GetPlayerNumericModel(string playerId)
        {
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                return currentBattle.GetPlayerNumericModel(playerId);
            }
            
            // 如果没有战斗数据，返回一个空的数值模型
            return new NumericModel();
        }

        /// <summary>
        /// 检查玩家是否有指定数值类型
        /// </summary>
        public bool HasPlayerNumeric(string playerId, int numericType)
        {
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                var numericModel = currentBattle.GetPlayerNumericModel(playerId);
                return numericModel.Contains(numericType);
            }
            return false;
        }

        /// <summary>
        /// 清除指定玩家的所有数值数据
        /// </summary>
        public void ClearPlayerNumeric(string playerId)
        {
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                var numericModel = currentBattle.GetPlayerNumericModel(playerId);
                numericModel.Clear();
            }
        }

        /// <summary>
        /// 清除所有玩家数值数据
        /// </summary>
        public void ClearAllPlayerNumeric()
        {
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                foreach (var numericModel in currentBattle.PlayerNumericModels.Values)
                {
                    numericModel.Clear();
                }
                currentBattle.PlayerNumericModels.Clear();
            }
        }

        /// <summary>
        /// 获取所有玩家ID列表
        /// </summary>
        public IEnumerable<string> GetAllPlayerIds()
        {
            var battleModel = this.GetModel<IClientBattleModel>();
            var currentBattle = battleModel?.GetCurrentBattle();
            
            if (currentBattle != null)
            {
                return currentBattle.PlayerNumericModels.Keys;
            }
            return new List<string>();
        }

        protected override void OnDeinit()
        {
            mUnregisterList?.UnRegisterAll();
        }
    }
} 
