using System;
using System.Collections.Generic;
using Client.Command.Camera;
using Client.Model.Character;
using Cysharp.Threading.Tasks;
using GameClient;
using GameClient.System;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace Client.System.Camera
{
    /// <summary>
    /// 角色状态枚举，支持扩展更多效果
    /// </summary>
    public enum CharacterEffectState
    {
        Normal,          // 正常状态
        SlowMotion,      // 慢动作
        FreezeFrame,     // 顿帧 (预留)
        HitStop,         // 卡肉 (预留)
        Rewind          // 回倒 (预留)
    }

    /// <summary>
    /// 人物骨骼数据控制系统 
    /// </summary>
    public class CharacterBonesController
    {
        public CharacterBones Bones => _bones; 
        public CharacterEffectState CurrentState => _currentEffectState;
        
        // 效果状态相关
        private CharacterEffectState _currentEffectState = CharacterEffectState.Normal;
        private float _timeScale = 1f;
        private float _accumulatedTime = 0.0f; 
        private float _deltaTime = 0.0f;
        private float _effectFrameTime = 0.0f;

        // 数据相关
        private CharacterBones _bones;
        private Queue<MovementData> _dataQueue;
        private MovementData _realTimeData;     // 实时数据，用于服务器上传
        private MovementData _displayData;      // 显示数据，受效果影响
        
        // 扩展效果预留
        private MovementData _frozenData;      // 顿帧时的固定数据
        private int _hitStopFramesRemaining;   // 卡肉剩余帧数
        
        public void SetAnimator(Animator anim, string id)
        {        
            // 初始化状态
            _currentEffectState = CharacterEffectState.Normal;
            _timeScale = 1f;
            _dataQueue = new();
            _hitStopFramesRemaining = 0;
            _bones = new CharacterBones(anim, id);
            
            // 调试：检查骨骼绑定情况
            int validBoneCount = 0;
            for (int i = 0; i < _bones.Bones.Length; i++)
            {
                if (_bones.Bones[i] != null)
                {
                    validBoneCount++;
                }
            }
            LogKit.I($"[CharacterBonesSystem] SetAnimator - ValidBones: {validBoneCount}/{_bones.Bones.Length}, Animator: {anim?.name}");
        }

        #region 效果状态控制
        /// <summary>
        /// 开始慢动作效果
        /// </summary>
        public async UniTask StartSlowMotion(float timeScale, float time)
        {
            CalculateEffectFrameTime();
            await UniTask.Delay(TimeSpan.FromSeconds(time));
            StopAllEffects();
        }
        /// <summary>
        /// 开始基于AnimationCurve的慢动作效果
        /// </summary>
        /// <param name="curve">动画曲线，X轴为时间进度(0-1)，Y轴为减速系数</param>
        /// <param name="duration">慢动作持续时间</param>
        public async UniTask StartSlowMotion(AnimationCurve curve, float duration)
        {
            if (curve == null)
            {
                LogKit.E("[CharacterBonesController] StartSlowMotion: AnimationCurve不能为空");
                return;
            }

            if (duration <= 0f)
            {
                LogKit.E($"[CharacterBonesController] StartSlowMotion: 持续时间必须大于0，当前值: {duration}");
                return;
            }

            _currentEffectState = CharacterEffectState.SlowMotion;
            _accumulatedTime = 0f;
            _dataQueue.Clear();

            float elapsedTime = 0f;
            LogKit.I($"[CharacterBonesController] 开始慢动作效果，持续时间: {duration}秒");

            while (elapsedTime < duration)
            {
                float normalizedTime = elapsedTime / duration;
                float curveValue = curve.Evaluate(normalizedTime);

                // 确保减速系数在合理范围内
                _timeScale = Mathf.Clamp(curveValue, 0.01f, 2f);
                CalculateEffectFrameTime();

                LogKit.I($"[CharacterBonesController] 慢动作进度: {normalizedTime:F2}, 减速系数: {_timeScale:F2}, 帧时间: {_effectFrameTime:F4}");

                await UniTask.Yield();
                elapsedTime += Time.unscaledDeltaTime;
            }

            LogKit.I("[CharacterBonesController] 慢动作效果结束");
            StopAllEffects();
        }

        /// <summary>
        /// 停止所有效果，恢复正常状态
        /// </summary>
        public void StopAllEffects()
        {
            LogKit.I("[CharacterBonesController] StopAllEffects");

            _currentEffectState = CharacterEffectState.Normal;
            _timeScale = 1.0f;
            _accumulatedTime = 0f;
            _hitStopFramesRemaining = 0;
            _dataQueue.Clear();
            _frozenData = null;
        }
        #endregion

        #region 数据更新核心逻辑
        /// <summary>
        /// 获取实时数据 - 始终返回最新的真实数据，用于服务器同步
        /// </summary>
        public MovementData GetRealTimeData()
        {
            return _realTimeData;
        }

        /// <summary>
        /// 获取显示数据 - 根据当前效果状态返回相应的显示数据
        /// </summary>
        public MovementData GetDisplayData()
        {
            return _displayData;
        }

        /// <summary>
        /// 兼容方法 - 返回显示数据
        /// </summary>
        public MovementData UpdateAndGetMovementData()
        {
            return GetDisplayData();
        }

        /// <summary>
        /// 数据更新循环 - 每帧调用
        /// </summary>
        public void UpdateDataLoop()
        {
            if (_bones == null)
            {
                LogKit.W("[CharacterBonesSystem] UpdateDataLoop called but _bones is null");
                return;
            }

            // 每帧都更新实时数据，不受任何效果影响
            _realTimeData = _bones.UpdatePositionAndRotationValues();

            // 根据当前效果状态更新显示数据和队列
            switch (_currentEffectState)
            {
                case CharacterEffectState.Normal:
                    _displayData = _realTimeData;
                    break;
                    
                case CharacterEffectState.SlowMotion:
                    _dataQueue.Enqueue(_realTimeData.DeepClone());
                    UpdateSlowMotionDisplayData();
                    break;
                    
                case CharacterEffectState.FreezeFrame:
                    _displayData = _frozenData ?? _displayData;
                    break;
                    
                case CharacterEffectState.HitStop:
                    _displayData = _frozenData ?? _displayData;
                    if (_hitStopFramesRemaining > 0)
                    {
                        _hitStopFramesRemaining--;
                        if (_hitStopFramesRemaining <= 0)
                        {
                            StopAllEffects();
                        }
                    }
                    break;
            }
        }

        /// <summary>
        /// 更新慢动作状态下的显示数据
        /// </summary>
        private void UpdateSlowMotionDisplayData()
        {
            if (!ShouldReleaseSlowMotionData() || _dataQueue.Count == 0)
            {
                LogKit.I("UpdateSlowMotionDisplayData Shouldn't Update");
                return;
            }
            
            LogKit.I("UpdateSlowMotionDisplayData Dequeue");
            _displayData = _dataQueue.Dequeue();
        }

        #endregion


        #region 辅助方法

        /// <summary>
        /// 计算效果的帧时间间隔
        /// </summary>
        private void CalculateEffectFrameTime()
        {
            var settingsModel = LaunchMainArch.Interface.GetModel<ClientSettingsModel>();
            
            if (float.TryParse(settingsModel.Data.MaximumFrameRate, out float targetFrameRate))
            {
                var targetFrameTime = 1.0f / targetFrameRate;
                _effectFrameTime = targetFrameTime / _timeScale;
            }
            else
            {
                // LogKit.E($"Failed to parse MaximumFrameRate: {settingsModel.Data.MaximumFrameRate}, using default 60fps");
                var frameTime  = (1.0f / 60.0f) ;
                _effectFrameTime = frameTime/ _timeScale;
            }
            
            LogKit.I($"[CharacterBonesSystem] CalculateEffectFrameTime - FrameTime: {_effectFrameTime:F4}s, TimeScale: {_timeScale}");
        }

        /// <summary>
        /// 判断是否应该释放慢动作数据
        /// </summary> 
        private bool ShouldReleaseSlowMotionData()
        {
            _accumulatedTime += Time.unscaledDeltaTime;
            
            if (_accumulatedTime >= _effectFrameTime)
            {
                _accumulatedTime %= _effectFrameTime;
                return true;
            }

            return false;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void RemoveAnimation()
        {
            StopAllEffects();
            _bones = null;
            ActionKit.OnLateUpdate.UnRegister(UpdateDataLoop);
            LogKit.I("[CharacterBonesSystem] RemoveAnimation - Resources cleaned up");
        }

        #endregion
    }
}
