using System.Collections.Generic;
using TFGShare.Protocol;

namespace GameClient.Event.BaseRoomEvents
{
    public class ClientRoomListUpdateEvent
    {
        public List<RoomData> CurrentClientRoomListData { get; private set; }
        public List<RoomData> OldClientRoomListData { get; private set; }
        public Dictionary<string, RoomSettingData> CurrentRoomSettings { get; private set; }

        //暴露的数据是深拷贝，防止外部篡改数据
        public ClientRoomListUpdateEvent(List<RoomData> currentClientRoomListData, List<RoomData> oldClientRoomListData,
            Dictionary<string, RoomSettingData> currentRoomSettings)
        {
            this.CurrentClientRoomListData = currentClientRoomListData;
            this.OldClientRoomListData = oldClientRoomListData;
            this.CurrentRoomSettings = currentRoomSettings;
        }
    }
}
