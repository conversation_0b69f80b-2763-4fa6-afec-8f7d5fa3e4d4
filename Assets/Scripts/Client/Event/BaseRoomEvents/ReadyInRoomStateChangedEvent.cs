using TFGShare.Protocol;

namespace GameClient.Event.BaseRoomEvents
{
    //当有玩家改变准备状态时，会发送此消息将服务器上的最新房间数据同步给本地数据model
    public class ReadyInRoomStateChangedEvent
    {
        public readonly RoomData RoomData;
        public readonly string RoomId;
        public readonly int TeamId;
        public readonly bool ToReady;

        public ReadyInRoomStateChangedEvent(RoomData roomData, string roomId, int teamId, bool toReady)
        {
            this.RoomData = roomData;
            this.RoomId = roomId;
            this.TeamId = teamId;
            this.ToReady = toReady;
        }
    }
}
