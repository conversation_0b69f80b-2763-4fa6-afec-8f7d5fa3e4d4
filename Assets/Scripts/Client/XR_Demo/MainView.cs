using System;
using TMPro;
using UnityEngine;
using XrReference;
using Button = UnityEngine.UI.Button;
using Random = UnityEngine.Random;

namespace GameClient.XR_Demo
{
    public class MainView : MonoBehaviour
    {
        public TMP_InputField HeightInput;
        public Button ConfirmBtn;

        private float _height;
        private bool _leftTriggerDown = false;
        private DateTime _startLeftTime;
        private bool _rightTriggerDown = false;
        private DateTime _startRightTime;
        private DateTime _lastTriggerTime;

        void Start()
        {
            HeightInput.onValueChanged.AddListener(HeightChanged);
            ConfirmBtn.onClick.AddListener(ResetHeight);

            InputEvent.Instance.onLeftTriggerEnter += () =>
            {
                _leftTriggerDown = true;
                _startLeftTime = DateTime.Now;
            };
            InputEvent.Instance.onRightTriggerEnter += () =>
            {
                _rightTriggerDown = true;
                _startRightTime = DateTime.Now;
            };

            InputEvent.Instance.onLeftTriggerUp += () =>
            {
                _leftTriggerDown = false;
            };
            InputEvent.Instance.onRightTriggerUp += () => { _rightTriggerDown = false; };

            InputEvent.Instance.onLeftTriggerDown += LeftTriggerDown;
        }



        void CheckDualTriggerHold()
        {
            if (DateTime.Now - _lastTriggerTime >= TimeSpan.FromSeconds(2))
            {
                float test = Random.Range(1f, 2f);
                Debug.Log($"触发身高校准。 身高 ： {test}");
                _lastTriggerTime = DateTime.Now;
                XRWrapper.Instance.CurrentDevice.CalibrateSkeleton(test);
            }
        }

        void HeightChanged(string value)
        {
            _height = float.Parse(value);
        }

        void ResetHeight()
        {
            XRWrapper.Instance.CurrentDevice.CalibrateSkeleton(_height);
        }

        void LeftTriggerDown()
        {

        }

        void RightTriggerDown()
        {

        }

        void Update()
        {
            if (_leftTriggerDown && _rightTriggerDown && (DateTime.Now - _startLeftTime).TotalSeconds >= 2 && (DateTime.Now - _startRightTime).TotalSeconds >= 2)
            {
                CheckDualTriggerHold();
            }
        }
    }
}
