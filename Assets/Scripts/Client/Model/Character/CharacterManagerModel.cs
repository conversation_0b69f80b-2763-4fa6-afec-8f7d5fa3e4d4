using System.Collections.Generic;
using GameShare.Common.Player;
using QFramework;
using UnityEngine;
using CharacterController = GameClient.CharacterController;
using NotImplementedException = System.NotImplementedException;

namespace Client.Model
{
    public class CharacterManagerModel : AbstractModel
    {
        public  Dictionary<string, CharacterController> CharacterDic => _characterDic;
        public CharacterSkillsExecutor SkillsExecutor => _skillsExecutor;
        public Dictionary<string, GameObject> CharacterPool => _characterPool; 
        private Dictionary<string, CharacterController> _characterDic;
        private CharacterSkillsExecutor _skillsExecutor;
        private Dictionary<string, GameObject> _characterPool;
        public Dictionary<string, PlayerLimbEnergy> CharacterLimbEnergy = new();
        
        protected override void OnInit()
        {
            _characterDic = new();
            _skillsExecutor = new();
            _characterPool = new(); 
        }


        /// <summary>
        /// 添加角色到管理器
        /// </summary>
        /// <param name="playerID">玩家ID</param>
        /// <param name="characterController">角色控制器</param>
        /// <returns>是否添加成功</returns>
        public bool AddCharacter(string playerID, CharacterController characterController)
        {
            if (string.IsNullOrEmpty(playerID))
            {
                LogKit.E($"[CharacterManagerModel] AddCharacter: playerID不能为空");
                return false;
            }

            if (characterController == null)
            {
                LogKit.E($"[CharacterManagerModel] AddCharacter: characterController不能为空, playerID: {playerID}");
                return false;
            }

            if (_characterDic.TryGetValue(playerID, out var existingController))
            {
                LogKit.W($"[CharacterManagerModel] AddCharacter: 玩家 {playerID} 已存在，将覆盖原有角色");
                _characterDic[playerID] = characterController;
                return true;
            }

            _characterDic.Add(playerID, characterController);
            LogKit.I($"[CharacterManagerModel] AddCharacter: 成功添加玩家 {playerID}");
            return true;
        }

        /// <summary>
        /// 从管理器中移除角色
        /// </summary>
        /// <param name="playerID">玩家ID</param>
        /// <returns>是否移除成功</returns>
        public bool RemoveCharacter(string playerID)
        {
            if (string.IsNullOrEmpty(playerID))
            {
                LogKit.E($"[CharacterManagerModel] RemoveCharacter: playerID不能为空");
                return false;
            }

            if (!_characterDic.TryGetValue(playerID, out var characterToRemove))
            {
                LogKit.W($"[CharacterManagerModel] RemoveCharacter: 玩家 {playerID} 不存在");
                return false;
            }

            var removed = _characterDic.Remove(playerID);
            if (removed)
            {
                LogKit.I($"[CharacterManagerModel] RemoveCharacter: 成功移除玩家 {playerID}");
            }
            return removed;
        }

        /// <summary>
        /// 添加角色预制件到对象池
        /// </summary>
        /// <param name="path">资源路径</param>
        /// <param name="prefab">预制件对象</param>
        /// <returns>是否添加成功</returns>
        public bool AddCharacterInPool(string path, GameObject prefab)
        {
            if (string.IsNullOrEmpty(path))
            {
                LogKit.E($"[CharacterManagerModel] AddCharacterInPool: path不能为空");
                return false;
            }

            if (prefab == null)
            {
                LogKit.E($"[CharacterManagerModel] AddCharacterInPool: prefab不能为空, path: {path}");
                return false;
            }

            if (_characterPool.TryGetValue(path, out var existingPrefab))
            {
                LogKit.W($"[CharacterManagerModel] AddCharacterInPool: 路径 {path} 已存在，将覆盖原有预制件");
                _characterPool[path] = prefab;
                return true;
            }

            _characterPool.Add(path, prefab);
            LogKit.I($"[CharacterManagerModel] AddCharacterInPool: 成功添加预制件 {path}");
            return true;
        }

        protected override void OnDeinit()
        {
            base.OnDeinit();
            _skillsExecutor = null; 
        }

        public CharacterController GetCharacter(string id)
        {
            return _characterDic.GetValueOrDefault(id);  
        }
    }
}
