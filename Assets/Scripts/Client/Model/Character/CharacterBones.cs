using QFramework;
using TFGShare.Protocol;
using UnityEngine;
using NotImplementedException = System.NotImplementedException;

namespace Client.Model.Character
{
    public class CharacterBones
    {
        public readonly Transform[] Bones;
        public readonly MovementData MovementData;
        
        // 添加Y轴偏移量属性，用于升空等特效
        public float YOffset { get; private set; } = 0f;
        
        public static readonly int BoneCount = GameShare.Common.Player.Movement.CharacterBones.BoneEnums.Length;

        private Transform characterOffset = null;

        public CharacterBones(Animator animator, string id)
        {
            Bones = new Transform[BoneCount]; 
            MovementData = new MovementData();
            MovementData.Datas = new(MovementData.VectorData Pos, MovementData.QuaternionData Rot)[BoneCount];

            for (int i = 0; i < GameShare.Common.Player.Movement.CharacterBones.BoneEnums.Length; ++i)
            {
                Transform bone = animator.GetBoneTransform(GameShare.Common.Player.Movement.CharacterBones.BoneEnums[i]);
                if (bone == null)
                {
                    continue;
                }

                Bones[i] = bone;
            }
            
            characterOffset = new GameObject($"CharacterOffset_{id}").transform;
            characterOffset.localPosition = Vector3.zero;
            characterOffset.localRotation = Quaternion.identity;
            characterOffset.localScale = Vector3.one;
        }

        /// <summary>
        /// 设置Y轴偏移量，用于升空等特效
        /// </summary>
        public void SetYOffset(float offset)
        {
            YOffset = offset;
        }

        /// <summary>
        /// 清除Y轴偏移量
        /// </summary>
        public void ClearYOffset()
        {
            YOffset = 0f;
        }

        public void UpdateByService(MovementData server)
        {
            if (server == null || server.Datas == null)
            {
                return;
            }

            if (server.Datas.Length < BoneCount)
            {
                return;
            }

            for (int i = 0; i < BoneCount; ++i)
            {
                Transform bone = Bones[i];
                if (bone == null)
                {
                    continue;
                }

                MovementData.Datas[i].Pos = new MovementData.VectorData 
                {
                    x = server.Datas[i].Pos.x,
                    y = server.Datas[i].Pos.y,
                    z = server.Datas[i].Pos.z
                };

                MovementData.Datas[i].Rot = new MovementData.QuaternionData
                {
                    x = server.Datas[i].Rot.x,
                    y = server.Datas[i].Rot.y,
                    z = server.Datas[i].Rot.z,
                    w = server.Datas[i].Rot.w,
                }; 
            }
        }
        

        public MovementData UpdatePositionAndRotationValues()
        {
            var curPos = characterOffset.localPosition;
            var curRot = characterOffset.localRotation;
            
            for (int i = 0; i < BoneCount; ++i)
            {
                Transform bone = Bones[i];
                if (bone == null)
                {
                    continue;
                }

                MovementData.Datas[i].Pos = new MovementData.VectorData 
                {
                    x = bone.position.x + curPos.x,
                    y = bone.position.y + curPos.y + YOffset, // 添加Y轴偏移量
                    z = bone.position.z + curPos.z
                };

                MovementData.Datas[i].Rot = new MovementData.QuaternionData
                {
                    x = bone.rotation.x + curRot.x,
                    y = bone.rotation.y + curRot.y,
                    z = bone.rotation.z + curRot.z,
                    w = bone.rotation.w + curRot.w,
                }; 
            }

            return MovementData;  
        }


        /// <summary>
        /// 设置骨骼位置和旋转值（待实现）
        /// </summary>
        /// <param name="data">移动数据</param>
        public void SetPositionAndRotationValues(MovementData data)
        {
            // TODO: 实现骨骼位置和旋转设置逻辑
            LogKit.W($"[CharacterBones] SetPositionAndRotationValues 方法尚未实现");
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public void Dispose()
        {
            if (characterOffset != null)
            {
                UnityEngine.Object.Destroy(characterOffset.gameObject);
                characterOffset = null;
            }

            // 清理骨骼引用
            for (int i = 0; i < Bones.Length; i++)
            {
                Bones[i] = null;
            }

            LogKit.I($"[CharacterBones] 资源已清理");
        }
    }
}
