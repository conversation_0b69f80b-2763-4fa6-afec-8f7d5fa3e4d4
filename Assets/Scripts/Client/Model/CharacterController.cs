using System;
using Client.Command.Camera;
using Client.Model.Character;
using Client.System.Camera;
using Cysharp.Threading.Tasks;
using GameClient.Entity;
using GameClient.Model;
using GameShare.Events;
using GameShare.Networking.Model;
using QFramework;
using TFGShare.Protocol;
using UnityEngine;

namespace GameClient
{
    /// <summary>
    /// 角色控制器 - 负责角色的初始化、动画播放、慢动作效果和数据同步
    /// </summary>
    public class CharacterController: ICanSendCommand
    {
        #region Properties
        public string ControllerName { get; set; }
        public CharacterEntity Entity { get; set; }
        public CharacterData Data { get; set; }
        public CharacterBones Bones => _bonesController?.Bones;
        public CharacterEffectState State => _bonesController?.CurrentState ?? CharacterEffectState.Normal;
        public bool Inited { get; private set; } = false;
        #endregion

        #region Private Fields
        private MovementData _movementCache;
        private CharacterBonesController _bonesController;
        #endregion

        #region Constructor
        public CharacterController()
        {
            Inited = false;
        }
        #endregion

        #region Initialization
        /// <summary>
        /// 初始化角色控制器
        /// </summary>
        /// <param name="entity">角色实体</param>
        /// <param name="data">角色数据</param>
        public void OnInit(CharacterEntity entity, CharacterData data)
        {
            if (entity == null)
            {
                LogKit.E("[CharacterController] OnInit: entity不能为空");
                return;
            }

            if (data == null)
            {
                LogKit.E("[CharacterController] OnInit: data不能为空");
                return;
            }

            try
            {
                _movementCache = null;
                _bonesController = new CharacterBonesController();

                var targetAnimator = data.IsLocal
                    ? LaunchMainArch.Interface.GetSystem<CharacterManagerSystem>().GetTargetAnimator()
                    : entity.Animator;

                _bonesController.SetAnimator(targetAnimator, data.ID);
                ActionKit.OnLateUpdate.Register(Tick);

                ControllerName = entity.name + data.ID;
                Entity = entity;
                Data = data;
                Inited = true;

                LogKit.I($"[CharacterController] 角色控制器初始化成功: {ControllerName}");
            }
            catch (Exception ex)
            {
                LogKit.E($"[CharacterController] OnInit失败: {ex.Message}");
                Inited = false;
            }
        }
        #endregion

        #region Animation Control
        /// <summary>
        /// 播放动画
        /// </summary>
        /// <param name="name">动画名称</param>
        public void PlayAnimation(string name)
        {
            if (string.IsNullOrEmpty(name))
            {
                LogKit.W("[CharacterController] PlayAnimation: 动画名称不能为空");
                return;
            }

            if (Entity == null)
            {
                LogKit.W("[CharacterController] PlayAnimation: Entity为空，无法播放动画");
                return;
            }

            Entity.StartPlayerAnimation(name);
            LogKit.I($"[CharacterController] 播放动画: {name}");
        }
        #endregion

        #region Slow Motion Control
        /// <summary>
        /// 开始基于AnimationCurve的慢动作效果
        /// </summary>
        /// <param name="curve">动画曲线</param>
        /// <param name="duration">持续时间</param>
        public void StartSlowMotion(AnimationCurve curve, float duration)
        {
            if (!ValidateSlowMotionPreconditions())
            {
                return;
            }

            if (curve == null)
            {
                LogKit.E("[CharacterController] StartSlowMotion: AnimationCurve不能为空");
                return;
            }

            if (duration <= 0f)
            {
                LogKit.E($"[CharacterController] StartSlowMotion: 持续时间必须大于0，当前值: {duration}");
                return;
            }

            _bonesController.StartSlowMotion(curve, duration).Forget();
            LogKit.I($"[CharacterController] 开始基于曲线的慢动作效果，持续时间: {duration}秒");
        }

        /// <summary>
        /// 开始固定比例的慢动作效果
        /// </summary>
        /// <param name="timeScale">时间缩放比例</param>
        /// <param name="time">持续时间</param>
        public void StartSlowMotion(float timeScale = 0.5f, float time = 10f)
        {
            if (!ValidateSlowMotionPreconditions())
            {
                return;
            }

            if (timeScale <= 0f)
            {
                LogKit.E($"[CharacterController] StartSlowMotion: 时间缩放比例必须大于0，当前值: {timeScale}");
                return;
            }

            if (time <= 0f)
            {
                LogKit.E($"[CharacterController] StartSlowMotion: 持续时间必须大于0，当前值: {time}");
                return;
            }

            _bonesController.StartSlowMotion(timeScale, time).Forget();
            LogKit.I($"[CharacterController] 开始固定比例慢动作效果，缩放比例: {timeScale}, 持续时间: {time}秒");
        }

        /// <summary>
        /// 停止慢动作效果
        /// </summary>
        public void PauseSlowMotion()
        {
            if (_bonesController == null)
            {
                LogKit.W("[CharacterController] PauseSlowMotion: _bonesController为空");
                return;
            }

            _bonesController.StopAllEffects();
            LogKit.I("[CharacterController] 慢动作效果已停止");
        }

        /// <summary>
        /// 验证慢动作前置条件
        /// </summary>
        /// <returns>是否满足条件</returns>
        private bool ValidateSlowMotionPreconditions()
        {
            if (_bonesController == null)
            {
                LogKit.E("[CharacterController] 慢动作操作失败: _bonesController为空");
                return false;
            }

            if (!Inited)
            {
                LogKit.E("[CharacterController] 慢动作操作失败: 控制器未初始化");
                return false;
            }

            return true;
        }
        #endregion

        #region Data Management
        /// <summary>
        /// 从服务器获取数据后进行刷新
        /// </summary>
        /// <param name="data">移动数据</param>
        public void UpdateMovementData(MovementData data)
        {
            if (data == null)
            {
                LogKit.W("[CharacterController] UpdateMovementData: 数据为空");
                return;
            }

            _movementCache = data;
        }

        /// <summary>
        /// 获取角色骨骼数据
        /// </summary>
        /// <returns>角色骨骼数据</returns>
        public CharacterBones GetBones()
        {
            if (_bonesController?.Bones == null)
            {
                LogKit.W("[CharacterController] GetBones: 骨骼数据为空");
                return null;
            }

            return _bonesController.Bones;
        }
        #endregion

        #region Update Loop
        /// <summary>
        /// 每帧更新逻辑
        /// </summary>
        private void Tick()
        {
            if (!Inited || _bonesController == null)
            {
                return;
            }

            try
            {
                _bonesController.UpdateDataLoop();

                if (Data.IsLocal)
                {
                    HandleLocalPlayerTick();
                }
                else if (_movementCache != null)
                {
                    HandleRemotePlayerTick();
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[CharacterController] Tick异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理本地玩家的每帧更新
        /// </summary>
        private void HandleLocalPlayerTick()
        {
            if (Data.CanUploadToServer)
            {
                UploadMovementDataToServer();
            }

            var displayData = _bonesController.GetDisplayData();
            if (displayData != null)
            {
                Entity?.CharacterDataConversion(displayData);
                SendMovementEvent(displayData);
            }
        }

        /// <summary>
        /// 处理远程玩家的每帧更新
        /// </summary>
        private void HandleRemotePlayerTick()
        {
            Entity?.CharacterDataConversion(_movementCache);
        }

        /// <summary>
        /// 上传移动数据到服务器
        /// </summary>
        private void UploadMovementDataToServer()
        {
            try
            {
                var networkModel = LaunchMainArch.Interface.GetModel<INetworkModel>();
                var realTimeData = _bonesController.GetRealTimeData();

                if (networkModel?.NetPeer != null && realTimeData != null)
                {
                    networkModel.SendPacket(networkModel.NetPeer, new C2SSyncMovementData
                    {
                        PlayerId = Data.ID,
                        MovementData = realTimeData,
                        CurDT = DateTime.Now,
                    }, null, LiteNetLib.DeliveryMethod.Sequenced);
                }
            }
            catch (Exception ex)
            {
                LogKit.E($"[CharacterController] 上传移动数据失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 发送移动事件
        /// </summary>
        /// <param name="movementData">移动数据</param>
        private void SendMovementEvent(MovementData movementData)
        {
            try
            {
                LaunchMainArch.Interface.SendEvent(new BattlePlayerMovementEvent
                {
                    PlayerId = Data.ID,
                    MovementData = movementData
                });
            }
            catch (Exception ex)
            {
                LogKit.E($"[CharacterController] 发送移动事件失败: {ex.Message}");
            }
        }
        #endregion

        #region ICanSendCommand Implementation
        public IArchitecture GetArchitecture()
        {
            return LaunchMainArch.Interface;
        }
        #endregion
    }
}
