namespace GameServer.Player.Events.FireHero
{
    /// <summary>
    /// 直接设置技能的阶段数
    /// </summary>
    public class FireHeroChangeStageDirectlyEvent
    {
        public string PlayerId { get; private set; }

        /// <summary>
        /// 改变的阶段数,可以为任意常数，包括负值 -1,1 etc
        /// </summary>
        public int ChangeStageCount = 1;
        
        public FireHeroChangeStageDirectlyEvent(string playerId, int changeStageCount)
        {
            PlayerId = playerId;
            ChangeStageCount = changeStageCount;
        }
    }
}
