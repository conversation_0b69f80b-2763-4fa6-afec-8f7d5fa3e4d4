using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.InputSystem;

public class SkeletonDebugInputs : MonoBehaviour
{
    private SkeletonSolver skeletonSolver;
    private SkeletonBonesRendering m_skeletonBonesRendering;
    private CharacterVisibilityController m_characterVisibilityController;

    public InputActionReference toggleVisibility;
    public InputActionReference switchCharacter;

    private void Start()
    {
        skeletonSolver = GetComponent<SkeletonSolver>();
        m_characterVisibilityController = FindObjectOfType<CharacterVisibilityController>();

        m_skeletonBonesRendering = skeletonSolver.SkeletonBonesRendering;


        toggleVisibility.action.performed += HandleToggleVisibility;
        switchCharacter.action.performed += HandleSwitchCharacter;
    }

    private void HandleSwitchCharacter(InputAction.CallbackContext obj)
    {
        m_characterVisibilityController.HandleOnSwitchCharacter();
    }

    private void HandleToggleVisibility(InputAction.CallbackContext obj)
    {
        Debug.Log($"Toggling Visibility");
        m_skeletonBonesRendering.ToggleVisibility();
    }


    private void OnDestroy()
    {
        toggleVisibility.action.performed -= HandleToggleVisibility;
        switchCharacter.action.performed -= HandleSwitchCharacter;
    }
}