using Avatar_Representation;
using Axis.Elements;
using Axis.Elements.AvatarRepresentation;
using Axis.Enumerations;
using System.Collections.Generic;
using UnityEngine;
using Object = UnityEngine.Object;

public class LegsSolver: BoneGroupSolver
{
    private readonly SerializableDictionary<NodeBinding, Transform> m_nodes;
    private UserBodyParameters m_UserBodyParameters;
    private NodesRelativeDirection m_nodesRelativeDirection;

    private BoolVariable enforceFeetAboveGroundByUpperLegOffset;
    private FloatVariable maxUpperLegOffsetForFeetGrouding;
    public LegsSolver(Transform hips, GroundSolver groundSolver, NodesRelativeDirection nodesRelativeDirection,
        AxisNodesRepresentation representation)
    {
        m_nodesRelativeDirection = nodesRelativeDirection;
        m_nodes = new SerializableDictionary<NodeBinding, Transform>
        {
            { NodeBinding.RightThigh, representation.nodesByLimb[NodeBinding.RightThigh].transform },
            { NodeBinding.LeftThigh, representation.nodesByLimb[NodeBinding.LeftThigh].transform },
            { NodeBinding.RightCalf, representation.nodesByLimb[NodeBinding.RightCalf].transform },
            { NodeBinding.LeftCalf, representation.nodesByLimb[NodeBinding.LeftCalf].transform }
        };
        Bones = new SerializableDictionary<HumanBodyBones, Transform>();

        Bones.Add(HumanBodyBones.Hips, hips);

        Bones.Add(HumanBodyBones.RightUpperLeg,
            JointCreator.CreateJointTransform("Right Thigh", Bones[HumanBodyBones.Hips]));
        Bones.Add(HumanBodyBones.LeftUpperLeg,
            JointCreator.CreateJointTransform("Left Thigh", Bones[HumanBodyBones.Hips]));
        //CreateSkeletonLimb(NodeBinding.LeftThigh, m_bones[HumanBodyBones.Hips]));
        Bones.Add(HumanBodyBones.RightLowerLeg,
            JointCreator.CreateJointTransform("Right Lower Leg", Bones[HumanBodyBones.RightUpperLeg]));
        Bones.Add(HumanBodyBones.LeftLowerLeg,
            JointCreator.CreateJointTransform("Left Lower Leg", Bones[HumanBodyBones.LeftUpperLeg]));
        Bones.Add(HumanBodyBones.RightFoot,
            JointCreator.CreateJointTransform("Right Foot", Bones[HumanBodyBones.RightLowerLeg]));
        Bones.Add(HumanBodyBones.LeftFoot,
            JointCreator.CreateJointTransform("Left Foot", Bones[HumanBodyBones.LeftLowerLeg]));

        requiredFootHeight = Resources.Load(SkeletonSolverFloatVariablesLoader.RequiredFootHeightPath) as FloatVariable;
        
        m_UserBodyParameters = ActiveUserBodyParameters.Instance.UserBodyParameters;
        
        enforceFeetAboveGroundByUpperLegOffset = Resources.Load(SkeletonSolverFloatVariablesLoader.EnforceFeetAboveGroundByUpperLegOffsetPath) as BoolVariable;
        maxUpperLegOffsetForFeetGrouding =
            Resources.Load(SkeletonSolverFloatVariablesLoader.MaxUpperLegOffsetForFeetGrounding) as FloatVariable;
        
    }

    public LegsSolver(Transform hips, NodesRelativeDirection nodesRelativeDirection)
    {
        m_nodesRelativeDirection = nodesRelativeDirection;
        var axisNodesRepresentation = Object.FindObjectOfType<AxisNodesRepresentation>();
        m_nodes = new SerializableDictionary<NodeBinding, Transform>
        {
            { NodeBinding.RightThigh, axisNodesRepresentation.nodesByLimb[NodeBinding.RightThigh].transform },
            { NodeBinding.LeftThigh, axisNodesRepresentation.nodesByLimb[NodeBinding.LeftThigh].transform },
            { NodeBinding.RightCalf, axisNodesRepresentation.nodesByLimb[NodeBinding.RightCalf].transform },
            { NodeBinding.LeftCalf, axisNodesRepresentation.nodesByLimb[NodeBinding.LeftCalf].transform }
        };

        
        Bones = new SerializableDictionary<HumanBodyBones, Transform>();

        Bones.Add(HumanBodyBones.Hips, hips);

        Bones.Add(HumanBodyBones.RightUpperLeg,
            JointCreator.CreateJointTransform("Right Thigh", Bones[HumanBodyBones.Hips]));
        Bones.Add(HumanBodyBones.LeftUpperLeg,
            JointCreator.CreateJointTransform("Left Thigh", Bones[HumanBodyBones.Hips]));
            //CreateSkeletonLimb(NodeBinding.LeftThigh, m_bones[HumanBodyBones.Hips]));
        Bones.Add(HumanBodyBones.RightLowerLeg,
            JointCreator.CreateJointTransform("Right Lower Leg", Bones[HumanBodyBones.RightUpperLeg]));
        Bones.Add(HumanBodyBones.LeftLowerLeg,
            JointCreator.CreateJointTransform("Left Lower Leg", Bones[HumanBodyBones.LeftUpperLeg]));
        Bones.Add(HumanBodyBones.RightFoot,
            JointCreator.CreateJointTransform("Right Foot", Bones[HumanBodyBones.RightLowerLeg]));
        Bones.Add(HumanBodyBones.LeftFoot,
            JointCreator.CreateJointTransform("Left Foot", Bones[HumanBodyBones.LeftLowerLeg]));
        
        requiredFootHeight = Resources.Load(SkeletonSolverFloatVariablesLoader.RequiredFootHeightPath) as FloatVariable;
        m_UserBodyParameters = ActiveUserBodyParameters.Instance.UserBodyParameters;
        enforceFeetAboveGroundByUpperLegOffset = Resources.Load(SkeletonSolverFloatVariablesLoader.EnforceFeetAboveGroundByUpperLegOffsetPath) as BoolVariable;
        maxUpperLegOffsetForFeetGrouding =
            Resources.Load(SkeletonSolverFloatVariablesLoader.MaxUpperLegOffsetForFeetGrounding) as FloatVariable;

    }

    public LegsSolver(Dictionary<HumanBodyBones, Transform> bones, NodesRelativeDirection nodesRelativeDirection)
    {
        m_nodesRelativeDirection = nodesRelativeDirection;
        var axisNodesRepresentation = Object.FindObjectOfType<AxisNodesRepresentation>();
        m_nodes = new SerializableDictionary<NodeBinding, Transform>
        {
            { NodeBinding.RightThigh, axisNodesRepresentation.nodesByLimb[NodeBinding.RightThigh].transform },
            { NodeBinding.LeftThigh, axisNodesRepresentation.nodesByLimb[NodeBinding.LeftThigh].transform },
            { NodeBinding.RightCalf, axisNodesRepresentation.nodesByLimb[NodeBinding.RightCalf].transform },
            { NodeBinding.LeftCalf, axisNodesRepresentation.nodesByLimb[NodeBinding.LeftCalf].transform }
        };


        Bones = new SerializableDictionary<HumanBodyBones, Transform>();

        Bones.Add(HumanBodyBones.Hips, bones[HumanBodyBones.Hips]);
        Bones.Add(HumanBodyBones.RightUpperLeg, bones[HumanBodyBones.RightUpperLeg]);
        Bones.Add(HumanBodyBones.RightLowerLeg, bones[HumanBodyBones.RightLowerLeg]);
        Bones.Add(HumanBodyBones.RightFoot, bones[HumanBodyBones.RightFoot]);
        Bones.Add(HumanBodyBones.LeftUpperLeg, bones[HumanBodyBones.LeftUpperLeg]);
        Bones.Add(HumanBodyBones.LeftLowerLeg, bones[HumanBodyBones.LeftLowerLeg]);
        Bones.Add(HumanBodyBones.LeftFoot, bones[HumanBodyBones.LeftFoot]);

        requiredFootHeight = Resources.Load(SkeletonSolverFloatVariablesLoader.RequiredFootHeightPath) as FloatVariable;
        m_UserBodyParameters = ActiveUserBodyParameters.Instance.UserBodyParameters;
        enforceFeetAboveGroundByUpperLegOffset = Resources.Load(SkeletonSolverFloatVariablesLoader.EnforceFeetAboveGroundByUpperLegOffsetPath) as BoolVariable;
        maxUpperLegOffsetForFeetGrouding =
            Resources.Load(SkeletonSolverFloatVariablesLoader.MaxUpperLegOffsetForFeetGrounding) as FloatVariable;
    }

    public override void Solve()
    {
        SolveThighPosition();
        SolveThighRotation();
        SolveCalfPosition();
        SolveCalfRotation();
        SolveFeetPosition();
        
        
    }
    
    public void SolveThighPosition()
    {
        var legOffset = m_UserBodyParameters
            .parametersGroups[ParametersGroupType.LegOffset].GetVector3(BodyPartParametertId.LegToHipsOffsetX,
                BodyPartParametertId.LegToHipsOffsetY, BodyPartParametertId.LegToHipsOffsetZ);

        legOffset *= scaleFactor.Value;
        
        
        Bones[HumanBodyBones.RightUpperLeg].localPosition = legOffset;

        legOffset.x *= -1;
        Bones[HumanBodyBones.LeftUpperLeg].localPosition = legOffset;
    }

    public void SolveCalfPosition()
    {
        var lowerLegLength = m_UserBodyParameters
            .parametersGroups[ParametersGroupType.AllParameters].GetParameterValue(BodyPartParametertId.UpperLegLength)
            .Value ;

        lowerLegLength *= scaleFactor.Value;
        
        
        Bones[HumanBodyBones.RightLowerLeg].localPosition = new Vector3(0f, lowerLegLength, 0f);

        Bones[HumanBodyBones.LeftLowerLeg].localPosition = new Vector3(0f, lowerLegLength, 0f);
    }

    public void SolveFeetPosition()
    {
        var lowerLegLength = m_UserBodyParameters
            .parametersGroups[ParametersGroupType.AllParameters].GetParameterValue(BodyPartParametertId.LowerLegLength)
            .Value;

        
        var lowerLegLengthScaled = lowerLegLength * scaleFactor.Value;
        Bones[HumanBodyBones.RightFoot].localPosition = new Vector3(0f, lowerLegLengthScaled, 0f);
        Bones[HumanBodyBones.LeftFoot].localPosition = new Vector3(0f, lowerLegLengthScaled, 0f);
    }

    public void SolveCalfRotation()
    {
        Bones[HumanBodyBones.RightLowerLeg].localEulerAngles = new Vector3(-CalculateAngleBetweenLegAndCalf(m_nodes[NodeBinding.RightCalf], m_nodes[NodeBinding.RightThigh]), 0f, 0f);
        Bones[HumanBodyBones.LeftLowerLeg].localEulerAngles = new Vector3(-CalculateAngleBetweenLegAndCalf(m_nodes[NodeBinding.LeftCalf], m_nodes[NodeBinding.LeftThigh]), 0f, 0f);
    }

    private float CalculateAngleBetweenLegAndCalf(Transform lowerLegNode, Transform upperLegNode)
    {
        var calfProjectedOnThighPlane = Vector3.ProjectOnPlane(
            lowerLegNode.forward,
            upperLegNode.right);

        var angleBetweenThighAndCalf = Vector3.SignedAngle(
            upperLegNode.forward,
            calfProjectedOnThighPlane,
            upperLegNode.right);
        
        return angleBetweenThighAndCalf;
    }



    public void SolveThighRotation()
    {
        
        
        
        (Vector3 rightThighForward, Vector3 rightThighUp) =
            m_nodesRelativeDirection.GetRelativeForwardAndUpVector(HumanBodyBones.RightUpperLeg);
        
        (Vector3 leftThighForward, Vector3 leftThighUp) =
            m_nodesRelativeDirection.GetRelativeForwardAndUpVector(HumanBodyBones.LeftUpperLeg);

        Bones[HumanBodyBones.RightUpperLeg].rotation = Quaternion.LookRotation(
            rightThighForward,
            -rightThighUp);
        
        Bones[HumanBodyBones.LeftUpperLeg].rotation = Quaternion.LookRotation(
            leftThighForward,
            -leftThighUp);
        

    }

    
    private FloatVariable requiredFootHeight;

    public void LateUpdate()
    {
        //CorrectFootBelowGround();
    }

    public void EnforceFootAboveGround()
    {
        if (enforceFeetAboveGroundByUpperLegOffset.Value == false) return;
        
        
        EnforceFootByUpperLegOffset(true);
        EnforceFootByUpperLegOffset(false);

        
    }

    private float rightLegCurrentOffset;
    private float leftLegCurrentOffset;

    private void EnforceFootByUpperLegOffset(bool isRightFoot)
    {
        var foot = Bones[isRightFoot ? HumanBodyBones.RightFoot : HumanBodyBones.LeftFoot];
        var lowestAnkleHeight = 0f + requiredFootHeight.Value;
        var footHeightDifference = lowestAnkleHeight - foot.position.y ;
        
        if (footHeightDifference > 0f)
        {
            
            float offset = Mathf.Clamp(footHeightDifference, 0f, maxUpperLegOffsetForFeetGrouding.Value);
            Bones[isRightFoot ? HumanBodyBones.RightUpperLeg : HumanBodyBones.LeftUpperLeg].position += new Vector3(0f, offset, 0f);

            if (isRightFoot == true)
            {
                rightLegCurrentOffset = offset;
            }
            else
            {
                leftLegCurrentOffset = offset;
            }
        }
    }
}