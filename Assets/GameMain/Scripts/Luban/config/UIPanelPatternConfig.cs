
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using <PERSON><PERSON>;
using Newtonsoft.Json.Linq;



namespace GameMainConfig.config
{

public sealed partial class UIPanelPatternConfig : Luban.BeanBase
{
    public UIPanelPatternConfig(JToken _buf) 
    {
        JObject _obj = _buf as JObject;
        Id = (int)_obj.GetValue("id");
        Pattern = (string)_obj.GetValue("pattern");
        ConfigId = (int)_obj.GetValue("configId");
        Description = (string)_obj.GetValue("description");
    }

    public static UIPanelPatternConfig DeserializeUIPanelPatternConfig(JToken _buf)
    {
        return new config.UIPanelPatternConfig(_buf);
    }

    /// <summary>
    /// id
    /// </summary>
    public readonly int Id;
    /// <summary>
    /// UI名称
    /// </summary>
    public readonly string Pattern;
    /// <summary>
    /// 配置id
    /// </summary>
    public readonly int ConfigId;
    /// <summary>
    /// 描述
    /// </summary>
    public readonly string Description;


    public const int __ID__ = -978494418;
    public override int GetTypeId() => __ID__;

    public  void ResolveRef(GameMainTables tables)
    {
    }

    public override string ToString()
    {
        return "{ "
        + "id:" + Id + ","
        + "pattern:" + Pattern + ","
        + "configId:" + ConfigId + ","
        + "description:" + Description + ","
        + "}";
    }
}
}

